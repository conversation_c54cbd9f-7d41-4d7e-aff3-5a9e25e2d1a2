"""
Portfolio Data Models
Core data structures for portfolio management and tracking
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from decimal import Decimal
from enum import Enum
from dataclasses import dataclass, field
from pydantic import BaseModel, Field, validator


class PositionType(str, Enum):
    """Position type enumeration."""
    LONG = "long"
    SHORT = "short"
    OPTION_LONG = "option_long"
    OPTION_SHORT = "option_short"


class AssetClass(str, Enum):
    """Asset class enumeration."""
    EQUITY = "equity"
    OPTION = "option"
    FUTURE = "future"
    BOND = "bond"
    CASH = "cash"
    CRYPTO = "crypto"


@dataclass
class PortfolioPosition:
    """Individual position within a portfolio."""
    symbol: str
    quantity: float
    average_cost: float
    current_price: float
    position_type: PositionType
    asset_class: AssetClass
    sector: Optional[str] = None
    industry: Optional[str] = None
    
    # Greeks (for options)
    delta: float = 0.0
    gamma: float = 0.0
    theta: float = 0.0
    vega: float = 0.0
    rho: float = 0.0
    
    # Timestamps
    opened_at: datetime = field(default_factory=datetime.now)
    last_updated: datetime = field(default_factory=datetime.now)
    
    @property
    def market_value(self) -> float:
        """Calculate current market value of position."""
        return self.quantity * self.current_price
    
    @property
    def cost_basis(self) -> float:
        """Calculate total cost basis of position."""
        return abs(self.quantity) * self.average_cost
    
    @property
    def unrealized_pnl(self) -> float:
        """Calculate unrealized profit/loss."""
        if self.position_type in [PositionType.LONG, PositionType.OPTION_LONG]:
            return self.market_value - self.cost_basis
        else:  # Short positions
            return self.cost_basis - self.market_value
    
    @property
    def unrealized_pnl_percent(self) -> float:
        """Calculate unrealized P&L as percentage."""
        if self.cost_basis == 0:
            return 0.0
        return (self.unrealized_pnl / self.cost_basis) * 100
    
    @property
    def weight(self) -> float:
        """Position weight (to be calculated by portfolio)."""
        return 0.0  # Will be set by portfolio calculation


@dataclass
class Portfolio:
    """Portfolio data structure."""
    id: str
    user_id: str
    name: str
    nav: float  # Net Asset Value
    cash_balance: float
    positions: List[PortfolioPosition] = field(default_factory=list)
    
    # Metadata
    created_at: datetime = field(default_factory=datetime.now)
    last_updated: datetime = field(default_factory=datetime.now)
    
    @property
    def total_market_value(self) -> float:
        """Calculate total market value of all positions."""
        return sum(pos.market_value for pos in self.positions)
    
    @property
    def total_cost_basis(self) -> float:
        """Calculate total cost basis of all positions."""
        return sum(pos.cost_basis for pos in self.positions)
    
    @property
    def total_unrealized_pnl(self) -> float:
        """Calculate total unrealized P&L."""
        return sum(pos.unrealized_pnl for pos in self.positions)
    
    @property
    def total_unrealized_pnl_percent(self) -> float:
        """Calculate total unrealized P&L percentage."""
        if self.total_cost_basis == 0:
            return 0.0
        return (self.total_unrealized_pnl / self.total_cost_basis) * 100
    
    @property
    def cash_percentage(self) -> float:
        """Calculate cash as percentage of NAV."""
        if self.nav == 0:
            return 0.0
        return (self.cash_balance / self.nav) * 100
    
    def get_position_weights(self) -> Dict[str, float]:
        """Calculate position weights as percentage of NAV."""
        weights = {}
        for position in self.positions:
            weight = (abs(position.market_value) / self.nav) * 100 if self.nav > 0 else 0.0
            weights[position.symbol] = weight
        return weights
    
    def get_sector_allocation(self) -> Dict[str, float]:
        """Calculate sector allocation as percentage of NAV."""
        sector_values = {}
        for position in self.positions:
            sector = position.sector or "Unknown"
            sector_values[sector] = sector_values.get(sector, 0.0) + abs(position.market_value)
        
        # Convert to percentages
        sector_allocation = {}
        for sector, value in sector_values.items():
            sector_allocation[sector] = (value / self.nav) * 100 if self.nav > 0 else 0.0
        
        return sector_allocation


@dataclass
class PortfolioMetrics:
    """Portfolio performance and risk metrics."""
    # Basic metrics
    total_return: float = 0.0
    total_return_percent: float = 0.0
    
    # Greeks
    total_delta: float = 0.0
    total_gamma: float = 0.0
    total_theta: float = 0.0
    total_vega: float = 0.0
    total_rho: float = 0.0
    
    # Risk metrics
    portfolio_beta: float = 0.0
    portfolio_volatility: float = 0.0
    sharpe_ratio: float = 0.0
    sortino_ratio: float = 0.0
    max_drawdown: float = 0.0
    var_95: float = 0.0  # Value at Risk (95%)
    cvar_95: float = 0.0  # Conditional Value at Risk (95%)
    
    # Performance metrics
    win_rate: float = 0.0
    profit_factor: float = 0.0
    average_win: float = 0.0
    average_loss: float = 0.0
    largest_win: float = 0.0
    largest_loss: float = 0.0
    
    # Concentration metrics
    concentration_ratio: float = 0.0  # Top 5 positions as % of portfolio
    herfindahl_index: float = 0.0  # Portfolio concentration index
    
    # Calculated timestamp
    calculated_at: datetime = field(default_factory=datetime.now)


class PortfolioRequest(BaseModel):
    """Request model for portfolio operations."""
    name: str = Field(..., min_length=1, max_length=100)
    initial_cash: float = Field(default=100000.0, gt=0)
    
    @validator('name')
    def validate_name(cls, v):
        """Validate portfolio name."""
        if not v.strip():
            raise ValueError("Portfolio name cannot be empty")
        return v.strip()


class PortfolioResponse(BaseModel):
    """Response model for portfolio data."""
    id: str
    user_id: str
    name: str
    nav: float
    cash_balance: float
    total_positions: int
    total_market_value: float
    total_unrealized_pnl: float
    total_unrealized_pnl_percent: float
    cash_percentage: float
    created_at: datetime
    last_updated: datetime
    
    class Config:
        """Pydantic configuration."""
        from_attributes = True


class PositionResponse(BaseModel):
    """Response model for position data."""
    symbol: str
    quantity: float
    average_cost: float
    current_price: float
    market_value: float
    unrealized_pnl: float
    unrealized_pnl_percent: float
    position_type: PositionType
    asset_class: AssetClass
    sector: Optional[str]
    industry: Optional[str]
    weight: float
    delta: float
    gamma: float
    theta: float
    vega: float
    rho: float
    opened_at: datetime
    last_updated: datetime
    
    class Config:
        """Pydantic configuration."""
        from_attributes = True


class PortfolioMetricsResponse(BaseModel):
    """Response model for portfolio metrics."""
    total_return: float
    total_return_percent: float
    total_delta: float
    total_gamma: float
    total_theta: float
    total_vega: float
    total_rho: float
    portfolio_beta: float
    portfolio_volatility: float
    sharpe_ratio: float
    sortino_ratio: float
    max_drawdown: float
    var_95: float
    cvar_95: float
    win_rate: float
    profit_factor: float
    concentration_ratio: float
    herfindahl_index: float
    calculated_at: datetime
    
    class Config:
        """Pydantic configuration."""
        from_attributes = True
