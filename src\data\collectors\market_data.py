"""
Market Data Collection Service
Real-time and historical market data collection from multiple sources
"""

import asyncio
import logging
import aiohttp
import yfinance as yf
import pandas as pd
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import json

from config.settings import get_settings
from data.storage.cache import CacheManager
from utils.helpers import retry_async


logger = logging.getLogger(__name__)
settings = get_settings()


@dataclass
class MarketDataPoint:
    """Single market data point."""
    symbol: str
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: int
    adjusted_close: Optional[float] = None


@dataclass
class OptionsChainData:
    """Options chain data structure."""
    symbol: str
    expiration_date: str
    strike: float
    option_type: str  # 'call' or 'put'
    bid: float
    ask: float
    last: float
    volume: int
    open_interest: int
    implied_volatility: float
    delta: Optional[float] = None
    gamma: Optional[float] = None
    theta: Optional[float] = None
    vega: Optional[float] = None
    rho: Optional[float] = None


class MarketDataCollector:
    """Comprehensive market data collection service."""
    
    def __init__(self):
        self.cache_manager = CacheManager()
        self.session: Optional[aiohttp.ClientSession] = None
        self.collection_active = False
        self.collection_tasks = []
        
    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    async def start_real_time_collection(self):
        """Start real-time data collection tasks."""
        if self.collection_active:
            logger.warning("Real-time collection already active")
            return
        
        self.collection_active = True
        self.session = aiohttp.ClientSession()
        
        # Start collection tasks
        self.collection_tasks = [
            asyncio.create_task(self._collect_market_data_loop()),
            asyncio.create_task(self._collect_options_data_loop()),
            asyncio.create_task(self._collect_fundamental_data_loop())
        ]
        
        logger.info("Started real-time market data collection")
    
    async def stop_collection(self):
        """Stop real-time data collection."""
        self.collection_active = False
        
        # Cancel all tasks
        for task in self.collection_tasks:
            task.cancel()
        
        # Wait for tasks to complete
        await asyncio.gather(*self.collection_tasks, return_exceptions=True)
        
        if self.session:
            await self.session.close()
        
        logger.info("Stopped real-time market data collection")
    
    @retry_async(max_retries=3, delay=1.0)
    async def get_real_time_quote(self, symbol: str) -> Optional[MarketDataPoint]:
        """Get real-time quote for a symbol."""
        try:
            # Use yfinance for real-time data (free)
            ticker = yf.Ticker(symbol)
            info = ticker.info
            
            if not info:
                logger.warning(f"No data available for {symbol}")
                return None
            
            current_price = info.get('currentPrice') or info.get('regularMarketPrice')
            if not current_price:
                logger.warning(f"No current price for {symbol}")
                return None
            
            return MarketDataPoint(
                symbol=symbol,
                timestamp=datetime.now(),
                open=info.get('regularMarketOpen', current_price),
                high=info.get('regularMarketDayHigh', current_price),
                low=info.get('regularMarketDayLow', current_price),
                close=current_price,
                volume=info.get('regularMarketVolume', 0),
                adjusted_close=current_price
            )
            
        except Exception as e:
            logger.error(f"Error getting real-time quote for {symbol}: {e}")
            return None
    
    async def get_historical_data(self, symbol: str, period: str = "1y") -> List[MarketDataPoint]:
        """Get historical market data."""
        try:
            ticker = yf.Ticker(symbol)
            hist = ticker.history(period=period)
            
            if hist.empty:
                logger.warning(f"No historical data for {symbol}")
                return []
            
            data_points = []
            for date, row in hist.iterrows():
                data_points.append(MarketDataPoint(
                    symbol=symbol,
                    timestamp=date.to_pydatetime(),
                    open=float(row['Open']),
                    high=float(row['High']),
                    low=float(row['Low']),
                    close=float(row['Close']),
                    volume=int(row['Volume']),
                    adjusted_close=float(row['Close'])
                ))
            
            return data_points
            
        except Exception as e:
            logger.error(f"Error getting historical data for {symbol}: {e}")
            return []
    
    @retry_async(max_retries=3, delay=1.0)
    async def get_options_chain(self, symbol: str) -> List[OptionsChainData]:
        """Get options chain data for a symbol."""
        try:
            ticker = yf.Ticker(symbol)
            
            # Get available expiration dates
            expirations = ticker.options
            if not expirations:
                logger.warning(f"No options available for {symbol}")
                return []
            
            options_data = []
            
            # Limit to first few expirations to avoid rate limits
            for expiration in expirations[:5]:
                try:
                    option_chain = ticker.option_chain(expiration)
                    
                    # Process calls
                    for _, call in option_chain.calls.iterrows():
                        options_data.append(OptionsChainData(
                            symbol=symbol,
                            expiration_date=expiration,
                            strike=float(call['strike']),
                            option_type='call',
                            bid=float(call.get('bid', 0)),
                            ask=float(call.get('ask', 0)),
                            last=float(call.get('lastPrice', 0)),
                            volume=int(call.get('volume', 0)),
                            open_interest=int(call.get('openInterest', 0)),
                            implied_volatility=float(call.get('impliedVolatility', 0))
                        ))
                    
                    # Process puts
                    for _, put in option_chain.puts.iterrows():
                        options_data.append(OptionsChainData(
                            symbol=symbol,
                            expiration_date=expiration,
                            strike=float(put['strike']),
                            option_type='put',
                            bid=float(put.get('bid', 0)),
                            ask=float(put.get('ask', 0)),
                            last=float(put.get('lastPrice', 0)),
                            volume=int(put.get('volume', 0)),
                            open_interest=int(put.get('openInterest', 0)),
                            implied_volatility=float(put.get('impliedVolatility', 0))
                        ))
                
                except Exception as e:
                    logger.error(f"Error processing expiration {expiration} for {symbol}: {e}")
                    continue
            
            return options_data
            
        except Exception as e:
            logger.error(f"Error getting options chain for {symbol}: {e}")
            return []
    
    async def get_fundamental_data(self, symbol: str) -> Dict[str, Any]:
        """Get fundamental data for a symbol."""
        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info
            
            if not info:
                return {}
            
            # Extract key fundamental metrics
            fundamental_data = {
                'symbol': symbol,
                'market_cap': info.get('marketCap'),
                'enterprise_value': info.get('enterpriseValue'),
                'pe_ratio': info.get('trailingPE'),
                'forward_pe': info.get('forwardPE'),
                'peg_ratio': info.get('pegRatio'),
                'price_to_sales': info.get('priceToSalesTrailing12Months'),
                'price_to_book': info.get('priceToBook'),
                'enterprise_to_revenue': info.get('enterpriseToRevenue'),
                'enterprise_to_ebitda': info.get('enterpriseToEbitda'),
                'profit_margins': info.get('profitMargins'),
                'operating_margins': info.get('operatingMargins'),
                'return_on_assets': info.get('returnOnAssets'),
                'return_on_equity': info.get('returnOnEquity'),
                'revenue': info.get('totalRevenue'),
                'revenue_per_share': info.get('revenuePerShare'),
                'quarterly_revenue_growth': info.get('quarterlyRevenueGrowth'),
                'gross_profits': info.get('grossProfits'),
                'free_cashflow': info.get('freeCashflow'),
                'operating_cashflow': info.get('operatingCashflow'),
                'earnings_growth': info.get('earningsGrowth'),
                'revenue_growth': info.get('revenueGrowth'),
                'debt_to_equity': info.get('debtToEquity'),
                'current_ratio': info.get('currentRatio'),
                'quick_ratio': info.get('quickRatio'),
                'sector': info.get('sector'),
                'industry': info.get('industry'),
                'beta': info.get('beta'),
                '52_week_high': info.get('fiftyTwoWeekHigh'),
                '52_week_low': info.get('fiftyTwoWeekLow'),
                'dividend_yield': info.get('dividendYield'),
                'payout_ratio': info.get('payoutRatio'),
                'timestamp': datetime.now().isoformat()
            }
            
            return fundamental_data
            
        except Exception as e:
            logger.error(f"Error getting fundamental data for {symbol}: {e}")
            return {}
    
    async def _collect_market_data_loop(self):
        """Background task for collecting market data."""
        symbols = ['SPY', 'QQQ', 'IWM', 'VIX', 'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA']
        
        while self.collection_active:
            try:
                for symbol in symbols:
                    if not self.collection_active:
                        break
                    
                    data = await self.get_real_time_quote(symbol)
                    if data:
                        # Cache the data
                        cache_key = f"market_data:{symbol}"
                        await self.cache_manager.set(cache_key, asdict(data), ttl=60)
                
                # Wait before next collection cycle
                await asyncio.sleep(settings.OPTIONS_DATA_REFRESH_INTERVAL)
                
            except Exception as e:
                logger.error(f"Error in market data collection loop: {e}")
                await asyncio.sleep(10)
    
    async def _collect_options_data_loop(self):
        """Background task for collecting options data."""
        symbols = ['SPY', 'QQQ', 'AAPL', 'MSFT', 'TSLA']
        
        while self.collection_active:
            try:
                for symbol in symbols:
                    if not self.collection_active:
                        break
                    
                    options_data = await self.get_options_chain(symbol)
                    if options_data:
                        # Cache the options data
                        cache_key = f"options_chain:{symbol}"
                        serializable_data = [asdict(opt) for opt in options_data]
                        await self.cache_manager.set(cache_key, serializable_data, ttl=300)
                
                # Wait before next collection cycle (longer for options)
                await asyncio.sleep(settings.OPTIONS_DATA_REFRESH_INTERVAL * 5)
                
            except Exception as e:
                logger.error(f"Error in options data collection loop: {e}")
                await asyncio.sleep(30)
    
    async def _collect_fundamental_data_loop(self):
        """Background task for collecting fundamental data."""
        symbols = ['SPY', 'QQQ', 'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA']
        
        while self.collection_active:
            try:
                for symbol in symbols:
                    if not self.collection_active:
                        break
                    
                    fundamental_data = await self.get_fundamental_data(symbol)
                    if fundamental_data:
                        # Cache the fundamental data
                        cache_key = f"fundamental_data:{symbol}"
                        await self.cache_manager.set(cache_key, fundamental_data, ttl=3600)
                
                # Wait before next collection cycle (much longer for fundamentals)
                await asyncio.sleep(3600)  # 1 hour
                
            except Exception as e:
                logger.error(f"Error in fundamental data collection loop: {e}")
                await asyncio.sleep(60)
